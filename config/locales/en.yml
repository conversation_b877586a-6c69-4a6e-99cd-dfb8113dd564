# Files in the config/locales directory are used for internationalization and
# are automatically loaded by Rails. If you want to use locales other than
# English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more about the API, please read the Rails Internationalization guide
# at https://guides.rubyonrails.org/i18n.html.
#
# Be aware that YAML interprets the following case-insensitive strings as
# booleans: `true`, `false`, `on`, `off`, `yes`, `no`. Therefore, these strings
# must be quoted to be interpreted as strings. For example:
#
#     en:
#       "yes": yup
#       enabled: "ON"

en:
  # Application-wide common translations
  common:
    actions:
      add: "Add %{model}"      
      show: "Show"
      edit: "Edit"
      delete: "Delete"
      save: "Save"
      cancel: "Cancel"
      back: "Back"
      refresh: "Refresh"
    confirmations:
      are_you_sure: "Are you sure?"
      delete_confirmation: "Are you sure you want to delete this %{item}?"
    responses:
      yes: "Yes"
      no: "No"
    status:
      published: "Published"
      draft: "Draft"
      active: "Active"
      inactive: "Inactive"
    fields:
      name: "Name"
      title: "Title"
      slug: "Slug"
      email: "Email"
      role: "Role"
      author: "Author"
      actions: "Actions"
      created_at: "Created at"
      updated_at: "Updated at"
    confirm:
      sure: "Are you sure?"
      sure2: "You are about to delete"
      sure3: "This action cannot be undone."

  # Navigation and layout
  navigation:
    sidebar:
      view_site: "View site"
      content: "Content"      

  # Page-specific translations
  pages:    
    dashboard:
      index:
        title: "Dashboard"
      pageviews:
        title: "Pageviews"
      top_pages:
        title: "Top Pages"
      page_path:
        title: "Page Path"
    settings:
      index:
        title: "Settings"
        site_name: "Site Name"
        site_description: "Site Description"
        site_light_theme: "Site Light Theme"
        site_dark_theme: "Site Dark Theme"
        site_default_locale: "Site Default Language"
        light_theme: "Light Theme"
        dark_theme: "Dark Theme"
        site_default_time_zone: "Site Default Time Zone"
        time_zone: "Time Zone"
        user_settings: "User Settings"
        site_settings: "Site Settings"
        pick_light_theme: "Pick a light theme"
        pick_dark_theme: "Pick a dark theme"
        save_site_settings: "Save Site Settings"
        save_user_settings: "Save User Settings"
    errors:
      not_found:
        title: "404 Not Found"
        heading: "Uh-oh!"
        message: "We can't find that page."        
      access_denied:
        title: "403 Forbidden"
        heading: "Access Denied"
        message: "You are not authorized to access this page."        

  # Resource-specific translations
  resources:
    posts:
      index:
        heading: "Posts"
        no_posts: "No posts found"
      dashboard_list:
        heading: "My Posts"
      new:
        heading: "New Post"
      edit:
        heading: "Edit Post"
      show:
        title: "Showing Post"
      list: "List Posts"

    categories:
      index:
        heading: "Categories"
        no_categories: "No categories found"
      dashboard_list:
        heading: "Categories"
      new:
        heading: "New Category"
      edit:
        heading: "Edit Category"
      list: "List Categories"

    tags:
      index:
        heading: "Tags"
        no_tags: "No tags found"
      dashboard_list:
        heading: "Tags"
      new:
        heading: "New Tag"
      edit:
        heading: "Edit Tag"
      list: "List Tags"

    users:
      index:
        heading: "Users"
        no_users: "No users found"
      new:
        heading: "New User"
      edit:
        heading: "Edit User"
      list: "List Users"

  # Flash messages
  flash:
    notices:
      user_created: "User created successfully!"
      user_updated: "User updated successfully!"
      user_destroyed: "User was successfully destroyed."
      post_created: "Post created successfully!"
      post_updated: "Post updated successfully!"
      post_destroyed: "Post was successfully destroyed."
      category_created: "Category created successfully!"
      category_updated: "Category updated successfully!"
      category_destroyed: "Category was successfully destroyed."
      tag_created: "Tag created successfully!"
      tag_updated: "Tag updated successfully!"
      tag_destroyed: "Tag was successfully destroyed."
      settings_updated: "Settings updated successfully."
    errors:
      oops: "Oops, something went wrong."
      user_not_created: "User could not be created."
      user_not_updated: "User could not be updated."      
      post_not_created: "Post could not be created."
      post_not_updated: "Post could not be updated."
      category_not_created: "Category could not be created."
      category_not_updated: "Category could not be updated."
    warnings:
      max_desc: "Maximum 160 characters"
      leave_password_blank: "Leave password fields blank to keep the current password."

  # Authorization and access control
  authorization:
    access_denied:
      message: "You are not authorized to access this page."
      title: "Access Denied"

  # ActiveRecord translations
  activerecord:
    models:
      post: "Post"
      category: "Category"
      tag: "Tag"
      user: "User"
    attributes:
      post:
        title: "Title"
        excerpt: "Excerpt"
        body: "Content"
        category: "Category"
        published: "Published"
        published_at: "Published at"
        featured_image: "Featured Image"
        seo_title: "SEO Title"
      category:
        name: "Name"
        slug: "Slug"
        description: "Description"
      tag:
        name: "Name"
        slug: "Slug"
      user:
        email: "Email"
        password: "Password"
        password_confirmation: "Password Confirmation"
        role: "Role"
        role_select: "Select a role"
        avatar: "Avatar"
        new_password: "New Password"
        new_password_placeholder: "New Password (optional)"

  # Date and time formats
  date:
    formats:
      default: "%Y-%m-%d"
      short: "%b %d"
      long: "%B %d, %Y"
    day_names: [Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday]
    abbr_day_names: [Sun, Mon, Tue, Wed, Thu, Fri, Sat]
    month_names: [~, January, February, March, April, May, June, July, August, September, October, November, December]
    abbr_month_names: [~, Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec]

  time:
    formats:
      default: "%a, %d %b %Y %H:%M:%S %z"
      short: "%d %b %H:%M"
      long: "%B %d, %Y %H:%M"