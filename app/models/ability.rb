# frozen_string_literal: true

class Ability
  include CanCan::Ability

  def initialize(user)
    # Define abilities for the user here. For example:
    #
    #   return unless user.present?
    #   can :read, :all
    #   return unless user.admin?
    #   can :manage, :all
    #
    # The first argument to `can` is the action you are giving the user
    # permission to do.
    # If you pass :manage it will apply to every action. Other common actions
    # here are :read, :create, :update and :destroy.
    #
    # The second argument is the resource the user can perform the action on.
    # If you pass :all it will apply to every resource. Otherwise pass a Ruby
    # class of the resource.
    #
    # The third argument is an optional hash of conditions to further filter the
    # objects.
    # For example, here the user can only update published articles.
    #
    #   can :update, Article, published: true
    #
    # See the wiki for details:
    # https://github.com/CanCanCommunity/cancancan/blob/develop/docs/define_check_abilities.md
    user ||= User.new # user not login

    if user.persisted?
      # All logged-in users can update their own user settings
      can :update, :user_settings
      can :read, :settings

      if user.admin?
        can :manage, :all # Admins can do everything
        can :update, :settings # Admins can update site settings
        # Admins can destroy any user except themselves
        can :destroy, User
        cannot :destroy, User, id: user.id
      elsif user.editor?
        can :manage, Post
        can :manage, Category
        can :read, :dashboard
        # Editors CANNOT update site settings
        cannot :update, :settings
        can :read, User
      elsif user.author?
        can :read, Post
        can :create, Post
        can :update, Post, user_id: user.id
        can :destroy, Post, user_id: user.id
        can :read, :dashboard
        # Authors CANNOT update site settings
        cannot :update, :settings
      elsif user.viewer?
        can :read, Post
        cannot :read, :dashboard
        # Viewers CANNOT update site settings
        cannot :update, :settings
      end
    else
      can :read, Post
      cannot :read, :settings
    end
  end
end
