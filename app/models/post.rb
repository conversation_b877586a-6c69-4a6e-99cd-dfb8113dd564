class Post < ApplicationRecord
  extend FriendlyId
  friendly_id :title, use: [ :slugged, :history ]

  belongs_to :user
  belongs_to :category
  has_one_attached :featured_image

  has_many :post_tags, dependent: :destroy
  has_many :tags, through: :post_tags

  validates :title, presence: true, length: { maximum: 70 }
  validates :excerpt, presence: true, length: { maximum: 160 }
  validates :body, presence: true
  validates :seo_title, length: { maximum: 70 }, allow_blank: true
  validates :category_id, presence: true

  before_validation :set_default_category, on: [ :create, :update ]
  before_save :set_publication_date

  scope :published, -> { where(published: true).where("published_at <= ?", Time.current) }

  def should_generate_new_friendly_id?
    will_save_change_to_title? || super
  end

  def effective_seo_title
    seo_title.presence || title
  end

  def effective_meta_description
    meta_description.presence || excerpt&.truncate(160)
  end

  private

  def set_default_category
    if self.category_id.blank?
      self.category = Category.find_or_create_by!(name: "Uncategorized")
    end
  end

  def set_publication_date
    if published? && published_at.blank?
      self.published_at = Time.current
    end
  end
end
