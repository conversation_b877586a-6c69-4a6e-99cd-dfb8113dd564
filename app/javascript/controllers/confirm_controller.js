import { Controller } from "@hotwired/stimulus"

// Confirm controller: opens shared DaisyUI modal, shows item name,
// submits delete via Fetch/XHR, processes turbo-stream responses,
// shows loading state and traps focus for accessibility.
const Turbo = window.Turbo

export default class extends Controller {
  static targets = ["modal", "confirmButton", "name"]

  connect() {
    this._previouslyFocused = null
    this._keydownHandler = this._handleKeydown.bind(this)
    this._focusableElements = null
  }

  open(event) {
    event.preventDefault()
    const url = event.currentTarget.dataset.url
    const method = event.currentTarget.dataset.method || 'delete'
    const name = event.currentTarget.dataset.name || ''

    const wrapper = this.hasModalTarget ? this.modalTarget : document.querySelector('[data-confirm-target]')
    if (wrapper) {
      wrapper.dataset.url = url
      wrapper.dataset.method = method
      wrapper.dataset.name = name
      const nameTarget = wrapper.querySelector('[data-confirm-target="name"]')
      if (nameTarget) nameTarget.textContent = name
    }

    const checkbox = document.getElementById('confirm_modal')
    if (checkbox) checkbox.checked = true

    // accessibility: trap focus
    this._trapFocus()
  }

  async confirm() {
    const wrapper = this.hasModalTarget ? this.modalTarget : document.querySelector('[data-confirm-target]')
    const url = wrapper && wrapper.dataset ? wrapper.dataset.url : null
    const method = wrapper && wrapper.dataset ? wrapper.dataset.method || 'delete' : 'delete'

    if (!url) {
      this._closeModal()
      return
    }

    // loading state
    if (this.hasConfirmButtonTarget) {
      this.confirmButtonTarget.disabled = true
      this.confirmButtonTarget.classList.add('loading')
      const spinner = this.confirmButtonTarget.querySelector('.confirm-spinner')
      if (spinner) spinner.classList.remove('hidden')
    }

    try {
      const tokenEl = document.querySelector('meta[name="csrf-token"]')
      const csrfToken = tokenEl ? tokenEl.getAttribute('content') : null
      const acceptHeader = 'text/vnd.turbo-stream.html, text/html, application/json'

      const resp = await fetch(url, {
        method: method.toUpperCase(),
        headers: Object.assign({
          'Accept': acceptHeader,
          'X-Requested-With': 'XMLHttpRequest'
        }, csrfToken ? { 'X-CSRF-Token': csrfToken } : {}),
        credentials: 'same-origin'
      })

      const contentType = resp.headers.get('content-type') || ''
      const text = await resp.text()

      if (resp.ok && contentType.includes('turbo-stream')) {
        if (Turbo && Turbo.renderStreamMessage) Turbo.renderStreamMessage(text)
      } else if (resp.redirected) {
        window.location = resp.url
      } else if (resp.ok && contentType.includes('html')) {
        window.location.reload()
      } else {
        window.location.reload()
      }
    } catch (err) {
      console.error('Delete request failed', err)
      window.location.reload()
    } finally {
      
      await new Promise((resolve) => setTimeout(resolve, 1500))
      if (this.hasConfirmButtonTarget) {
        this.confirmButtonTarget.disabled = false
        this.confirmButtonTarget.classList.remove('loading')
        const spinner = this.confirmButtonTarget.querySelector('.confirm-spinner')
        if (spinner) spinner.classList.add('hidden')
      }
      this._closeModal()
    }
  }

  cancel() {
    this._closeModal()
  }

  _trapFocus() {
    const wrapper = this.hasModalTarget ? this.modalTarget : document.querySelector('[data-confirm-target]')
    if (!wrapper) return
    const dialog = wrapper.querySelector('.modal-box')
    if (!dialog) return

    this._previouslyFocused = document.activeElement
    const focusable = dialog.querySelectorAll('a[href], button:not([disabled]), textarea, input, select, [tabindex]:not([tabindex="-1"])')
    this._focusableElements = Array.prototype.slice.call(focusable)
    if (this._focusableElements.length > 0) this._focusableElements[0].focus()

    document.addEventListener('keydown', this._keydownHandler)
    dialog.setAttribute('role', 'dialog')
    dialog.setAttribute('aria-modal', 'true')
  }

  _handleKeydown(e) {
    if (e.key === 'Escape') {
      e.preventDefault()
      this._closeModal()
      return
    }
    if (e.key === 'Tab' && this._focusableElements && this._focusableElements.length) {
      const first = this._focusableElements[0]
      const last = this._focusableElements[this._focusableElements.length - 1]
      if (e.shiftKey) {
        if (document.activeElement === first) {
          e.preventDefault()
          last.focus()
        }
      } else {
        if (document.activeElement === last) {
          e.preventDefault()
          first.focus()
        }
      }
    }
  }

  _closeModal() {
    const checkbox = document.getElementById('confirm_modal')
    if (checkbox) checkbox.checked = false
    if (this._previouslyFocused) this._previouslyFocused.focus()
    document.removeEventListener('keydown', this._keydownHandler)
    this._focusableElements = null
  }
}
