class CategoriesController < ApplicationController
  before_action :authorize_categories, only: [ :dashboard_list, :create, :update, :destroy ]
  before_action :authenticate_user!, except: [ :index, :show ]
  before_action :set_category, only: %i[ show edit update destroy ]
  before_action :set_categories_breadcrumbs, except: [ :index, :show ]
  layout "dashboard", except: [ :index, :show ]

  def dashboard_list
    authorize! :read, Category
    set_meta_tags title: translate_resource_heading(:category, :dashboard_list)
    @pagy, @categories = pagy(Category.order(created_at: :desc), limit: 10)
    @category = Category.new
  end

  # GET /categories or /categories.json
  def index
    @categories = Category.all
  end

  # GET /categories/1 or /categories/1.json
  def show
  end

  # GET /categories/new
  def new
    authorize! :create, Category
    @category = Category.new
  end

  # GET /categories/1/edit
  def edit
    # If requested as a modal (modal=1) or via XHR, render only the form partial
    if params[:modal].present? || request.xhr?
      render partial: "form", locals: { category: @category }
    end
  end

  # POST /categories or /categories.json
  def create
    @category = Category.new(category_params)

    respond_to do |format|
      if @category.save
        format.turbo_stream do
          flash.now[:notice] = translate_flash_notice(:created, :category)
        end
        format.html { redirect_to dashboard_categories_path, notice: translate_flash_notice(:created, :category) }
        format.json { render :show, status: :created, location: @category }
      else
        format.turbo_stream do
          render turbo_stream: turbo_stream.replace(dom_id(@category, :form), partial: "form", locals: { category: @category })
        end
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @category.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /categories/1 or /categories/1.json
  def update
    respond_to do |format|
      if @category.update(category_params)
        format.turbo_stream do
          flash.now[:notice] = translate_flash_notice(:updated, :category)
          render :update
        end
        format.html { redirect_to dashboard_categories_path, notice: translate_flash_notice(:updated, :category) }
        format.json { render :show, status: :ok, location: @category }
      else
        format.turbo_stream { render turbo_stream: turbo_stream.replace(dom_id(@category, :form), partial: "form", locals: { category: @category }) }
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @category.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /categories/1 or /categories/1.json
  def destroy
    @category.destroy!

    respond_to do |format|
      format.html { redirect_to dashboard_categories_path, notice: translate_flash_notice(:destroyed, :category), status: :see_other }
      format.json { head :no_content }
    end
  end

  private
    def authorize_categories
      authorize! :read, :category
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_category
      @category = Category.friendly.find(params[:slug])
    end

    # Only allow a list of trusted parameters through.
    def category_params
      params.require(:category).permit(:name, :slug, :description)
    end

    def set_categories_breadcrumbs
      helpers.add_breadcrumb translate_page_title(:dashboard), dashboard_root_path
      if action_name == "dashboard_list"
        helpers.add_breadcrumb translate_resource_heading(:category, :dashboard_list), nil
      else
        helpers.add_breadcrumb translate_resource_heading(:category, :dashboard_list), dashboard_categories_path
      end

      case action_name
      when "new", "create"
        helpers.add_breadcrumb translate_resource_heading(:category, :new), nil
      when "edit", "update"
        helpers.add_breadcrumb @category.name, nil if @category
      end
    end
end
