class PostsController < ApplicationController
  before_action :authorize_posts, only: [ :dashboard_list, :create, :update, :destroy ]
  before_action :authenticate_user!, except: [ :index, :show ]
  before_action :set_post, only: %i[ show edit update destroy ]
  before_action :set_posts_breadcrumbs, except: [ :index, :show ]
  layout "dashboard", except: [ :index, :show ]

  # GET /posts or /posts.json
  def index
    @posts = Post.published.order(published_at: :desc)
  end

  # GET /posts/1 or /posts/1.json
  def show
    if request.path != post_path(@post)
      redirect_to @post, status: :moved_permanently
    end

    image_url = url_for(@post.featured_image) if @post.featured_image.attached?

    set_meta_tags(
      title: @post.effective_seo_title,
      description: @post.effective_meta_description,
      canonical: post_url(@post),
      og: {
        type: "article",
        image: image_url
      }.compact,
      twitter: {
        card: "summary_large_image",
        image: image_url
      }.compact
    )
  end

  def dashboard_list
    authorize! :read, Post
    set_meta_tags title: translate_resource_heading(:post, :dashboard_list)
    user = current_user
    if user.admin? || user.editor?
      @pagy, @posts = pagy(Post.all.order(published_at: :desc).includes(:user), limit: 10)
    else
      @pagy, @posts = pagy(Post.where(user: user).order(published_at: :desc), limit: 10)
    end
  end

  # GET /posts/new
  def new
    authorize! :create, Post
    set_meta_tags title: translate_resource_heading(:post, :new)
    @post = Post.new
  end

  # GET /posts/1/edit
  def edit
    authorize! :update, @post
    set_meta_tags title: translate_resource_heading(:post, :edit)
  end

  # POST /posts or /posts.json
  def create
    @post = Post.new(post_params)
    authorize! :create, @post
    @post.user = current_user

    respond_to do |format|
      if @post.save
        format.html { redirect_to @post, notice: translate_flash_notice(:created, :post) }
        format.json { render :show, status: :created, location: @post }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @post.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /posts/1 or /posts/1.json
  def update
    authorize! :update, @post
    respond_to do |format|
      if @post.update(post_params)
        format.html { redirect_to @post, notice: translate_flash_notice(:updated, :post) }
        format.json { render :show, status: :ok, location: @post }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @post.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /posts/1 or /posts/1.json
  def destroy
    authorize! :destroy, @post
    @post.destroy!

    respond_to do |format|
      format.html { redirect_to dashboard_posts_path, notice: translate_flash_notice(:destroyed, :post), status: :see_other }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_post
      @post = Post.friendly.find(params[:slug])
    end

    # Only allow a list of trusted parameters through.
    def post_params
      params.require(:post).permit(:title, :slug, :excerpt, :body, :seo_title, :meta_description, :published, :published_at, :featured_image, :category_id)
    end

    def authorize_posts
      authorize! :read, :post
    end

    def set_posts_breadcrumbs
      helpers.add_breadcrumb translate_page_title(:dashboard), dashboard_root_path

      if action_name == "dashboard_list"
        helpers.add_breadcrumb translate_resource_heading(:post, :dashboard_list), nil
      else
        helpers.add_breadcrumb translate_resource_heading(:post, :dashboard_list), dashboard_posts_path
      end

      case action_name
      when "new", "create"
        helpers.add_breadcrumb translate_resource_heading(:post, :new), nil
      when "edit", "update"
        helpers.add_breadcrumb @post.title, nil if @post
      end
    end
end
