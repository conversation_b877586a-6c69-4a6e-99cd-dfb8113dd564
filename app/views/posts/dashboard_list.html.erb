<div class="card bg-base-100 mt-4">
    <div class="card-body overflow-x-auto">
        <div class="card-actions">
            <h1 class="text-2xl font-bold mb-4"><%= translate_resource_heading(:post, :dashboard_list) %></h1>
            <%= link_to translate_resource_heading(:post, :new), new_dashboard_post_path, class: "btn btn-outline btn-dash btn-primary" %>
        </div>
        <table class="table">
            <thead>
                <tr>
                    <th><%= translate_field(:title) %></th>
                    <th><%= translate_field(:excerpt) %></th>
                    <th><%= translate_field(:category) %></th>
                    <th><%= translate_field(:published) %></th>
                    <th><%= translate_field(:published_at) %></th>
                    <th><%= translate_field(:author) %></th>
                    <th><%= translate_field(:actions) %></th>
            </thead>
            <tbody>
                <% @posts.each do |post| %>
                    <tr>
                        <td>
                            <% if post.title.length > 20 %>
                                <span class="tooltip tooltip-primary tooltip-right" data-tip="<%= post.title %>"><%= truncate(post.title, length: 20) %></span>
                            <% else %>
                                <%= post.title %>
                            <% end %>
                        </td>
                        <td><%= post.excerpt %></td>
                        <td><%= post.category.name %></td>
                        <td><%= translate_boolean(post.published?) %></td>
                        <td><%= localize_datetime(post.published_at) %></td>
                        <td><%= post.user.email %></td>
                        <td class="flex gap-2">
                            <%= link_to translate_action(:show), post, class: "btn btn-dash btn-primary", target: "_blank" %>
                            <%= link_to translate_action(:edit), edit_dashboard_post_path(post), class: "btn btn-dash btn-secondary" %>
                            <button type="button" class="btn btn-dash btn-error" data-action="click->confirm#open" data-url="<%= dashboard_post_path(post) %>" data-method="delete" data-name="<%= post.title %>">
                                <%= translate_action(:delete) %>
                            </button>
                        </td>
                    </tr>
                <% end %>
            </tbody>
        </table>      
        <%= render "partials/pagination", pagy: @pagy if @pagy.pages > 1 %>  
    </div>        
</div>