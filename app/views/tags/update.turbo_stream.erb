<%# Replace the updated row in the list %>
<%= turbo_stream.replace dom_id(@tag) do %>
  <tr id="<%= dom_id(@tag) %>">
    <td><%= @tag.name %></td>
    <td class="flex gap-2">
      <%= link_to translate_action(:show), @tag, class: "btn btn-dash btn-primary", target: "_blank" %>
      <button type="button" class="btn btn-dash btn-secondary" data-action="click->modal-remote#open" data-url="<%= edit_dashboard_tag_path(@tag, modal: 1) %>"><%= translate_action(:edit) %></button>
      <button type="button" class="btn btn-dash btn-error" data-action="click->confirm#open" data-url="<%= dashboard_tag_path(@tag) %>" data-method="delete" data-name="<%= @tag.name %>"><%= translate_action(:delete) %></button>
    </td>
  </tr>
<% end %>

<%# Close the remote modal and inject rendered alerts into the page %>
<%= turbo_stream.append "tags" do %>
  <script>
    (function(){
      var chk = document.getElementById('remote_modal_toggle');
      if (chk) chk.checked = false;
      var container = document.createElement('div');
      container.innerHTML = "<%= j render(partial: 'partials/alerts') %>";
      while (container.firstChild) { document.body.appendChild(container.firstChild); }
      // Highlight the updated row briefly
      var row = document.getElementById('<%= dom_id(@tag) %>');
      if (row) {
        row.classList.add('bg-success','transition-colors','duration-700');
        setTimeout(function(){ row.classList.remove('bg-success','transition-colors','duration-700'); }, 1500);
      }
    })();
  </script>
<% end %>
