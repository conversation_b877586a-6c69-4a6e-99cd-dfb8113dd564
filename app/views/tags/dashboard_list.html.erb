<div class="card bg-base-100 mt-4">
    <div class="card-body overflow-x-auto">        
        <div class="card-actions">
            <h1 class="text-2xl font-bold mb-4"><%= translate_resource_heading(:tag, :dashboard_list) %></h1>
            <label for="new_tag_modal" class="btn btn-dash btn-primary"><%= translate_action(:add, Tag) %></label>
        </div>
        <table class="table">
            <thead>
                <tr>
                    <th><%= translate_field(:name) %></th>                                        
                    <th><%= translate_field(:actions) %></th>
                </tr>
            </thead>
            <tbody id="tags">
                <% @tags.each do |tag| %>
                    <tr id="<%= dom_id(tag) %>">
                        <td><%= tag.name %></td>                                                
                        <td class="flex gap-2">
                            <%= link_to translate_action(:show), tag, class: "btn btn-dash btn-primary", target: "_blank" %>
                            <button type="button" class="btn btn-dash btn-secondary" data-action="click->modal-remote#open" data-url="<%= edit_dashboard_tag_path(tag, modal: 1) %>">
                                <%= translate_action(:edit) %>
                            </button>
                            <button type="button" class="btn btn-dash btn-error" data-action="click->confirm#open" data-url="<%= dashboard_tag_path(tag) %>" data-method="delete" data-name="<%= tag.name %>">
                                <%= translate_action(:delete) %>
                            </button>
                        </td>
                    </tr>
                <% end %>
            </tbody>
        </table>
        <%= render "partials/pagination", pagy: @pagy if @pagy.pages > 1 %>
    </div>        
</div>

<input type="checkbox" id="new_tag_modal" class="modal-toggle" />
<div class="modal" role="dialog">
  <div class="modal-box">
    <h3 class="font-bold text-lg"><%= translate_resource_heading(:tag, :new) %></h3>
    <div class="py-4">
        <%= render "form", tag: @tag %>
    </div>
  </div>
  <label class="modal-backdrop" for="new_tag_modal">Close</label>
</div>