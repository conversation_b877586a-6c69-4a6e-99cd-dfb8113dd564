<%= turbo_frame_tag dom_id(tag, :form) do %>
<%= form_with(model: [ :dashboard, tag ]) do |form| %>
  <% if tag.errors.any? %>
    <div role="alert" class="alert alert-error mb-4">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2 2m2-2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
      <div>
        <h3 class="font-bold"><%= pluralize(tag.errors.count, "error") %> prohibited this tag from being saved:</h3>
        <ul class="list-disc ml-5">
        <% tag.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
        </ul>
      </div>
    </div>
  <% end %>

  <div class="form-control">
    <%= form.label :name, class: "label" %>
    <%= form.text_field :name, class: "input input-bordered" %>
  </div>

  <div class="modal-action">
    <%= form.submit translate_action(:save), class: "btn btn-primary" %>
  </div>
<% end %>
<% end %>
