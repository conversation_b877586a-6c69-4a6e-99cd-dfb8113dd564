<%# Prepend the new tag to the list %>
<%= turbo_stream.prepend "tags" do %>
    <tr id="<%= dom_id(@tag) %>">
        <td><%= @tag.name %></td>
        <td class="flex gap-2">
            <%= link_to translate_action(:show), @tag, class: "btn btn-dash btn-primary", target: "_blank" %>
            <button type="button" class="btn btn-dash btn-secondary" data-action="click->modal-remote#open" data-url="<%= edit_dashboard_tag_path(@tag, modal: 1) %>"><%= translate_action(:edit) %></button>
                        <button type="button" class="btn btn-dash btn-error" data-action="click->confirm#open" data-url="<%= dashboard_tag_path(@tag) %>" data-method="delete" data-name="<%= @tag.name %>">
                            <%= translate_action(:delete) %>
                        </button>
        </td>
    </tr>
<% end %>

<%# Reset the form and close the modal %>
<%= turbo_stream.update dom_id(Tag.new, :form), partial: "form", locals: { tag: Tag.new } %>
<%# Close the modal and inject the rendered alerts partial into the page (no layout changes required) %>
<%= turbo_stream.append "tags" do %>
    <script>
        (function(){
            var chk = document.getElementById('new_tag_modal');
            if (chk) chk.checked = false;
            var container = document.createElement('div');
            container.innerHTML = "<%= j render(partial: 'partials/alerts') %>";
            while (container.firstChild) { document.body.appendChild(container.firstChild); }
        })();
    </script>
<% end %>
