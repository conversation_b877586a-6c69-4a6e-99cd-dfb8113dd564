<%= form_with(model: category, url: category.persisted? ? dashboard_category_path(category) : dashboard_categories_path, class: "contents") do |form| %>
  <% if category.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(category.errors.count, "error") %> prohibited this category from being saved:</h2>

      <ul>
        <% category.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>
  <div class="mt-2">
    <%= form.label :name, translate_field(:name), class: "block text-sm font-medium mb-1 text-left"  %>
    <%= form.text_field :name, class: "input input-primary w-full" %>
  </div>  
  <div class="mt-2">
    <%= form.label :description, translate_field(:description), class: "block text-sm font-medium mb-1 text-left"  %>
    <%= form.textarea :description, class: "textarea textarea-primary w-full" %>
  </div>
  <div class="mt-2">
    <%= form.submit nil, class: "btn btn-dash btn-primary" %>
    <%= link_to translate_action(:back), dashboard_categories_path, class: "btn btn-dash btn-secondary" %>
  </div>
<% end %>
