<div class="card bg-base-100 mt-4">
    <div class="card-body overflow-x-auto">        
        <div class="card-actions">
            <h1 class="text-2xl font-bold mb-4"><%= translate_resource_heading(:category, :dashboard_list) %></h1>
            <label for="new_category_modal" class="btn btn-dash btn-primary"><%= translate_action(:add, Category) %></label>
        </div>
        <table class="table">
            <thead>
                <tr>
                    <th><%= translate_field(:name) %></th>                    
                    <th><%= translate_field(:description) %></th>
                    <th><%= translate_field(:actions) %></th>
                </tr>
            </thead>
            <tbody id="categories">
                <% @categories.each do |cat| %>
                    <tr id="<%= dom_id(cat) %>">
                        <td><%= cat.name %></td>
                        <td><%= cat.description %></td>
                        <td class="flex gap-2">
                            <%= link_to translate_action(:show), cat, class: "btn btn-dash btn-primary", target: "_blank" %>
                            <button type="button" class="btn btn-dash btn-secondary" data-action="click->modal-remote#open" data-url="<%= edit_dashboard_category_path(cat, modal: 1) %>">
                                <%= translate_action(:edit) %>
                            </button>
                            <button type="button" class="btn btn-dash btn-error" data-action="click->confirm#open" data-url="<%= dashboard_category_path(cat) %>" data-method="delete" data-name="<%= cat.name %>">
                                <%= translate_action(:delete) %>
                            </button>
                        </td>
                    </tr>
                <% end %>
            </tbody>
        </table>
        <%= render "partials/pagination", pagy: @pagy if @pagy.pages > 1 %>
    </div>
</div>

<input type="checkbox" id="new_category_modal" class="modal-toggle" />
<div class="modal" role="dialog">
  <div class="modal-box">
    <h3 class="font-bold text-lg"><%= translate_resource_heading(:category, :new) %></h3>
    <div class="py-4">
        <%= render "form", category: @category %>
    </div>
  </div>
  <label class="modal-backdrop" for="new_category_modal">Close</label>
</div>