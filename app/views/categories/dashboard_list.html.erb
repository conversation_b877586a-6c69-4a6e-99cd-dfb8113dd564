<div class="card bg-base-100 mt-4">
    <div class="card-body overflow-x-auto">        
        <div class="card-actions">
            <h1 class="text-2xl font-bold mb-4"><%= translate_resource_heading(:category, :dashboard_list) %></h1>
            <%= link_to translate_action(:add, Category), new_dashboard_category_path, class: "btn btn-dash btn-primary" %>
        </div>
        <table class="table">
            <thead>
                <tr>
                    <th><%= translate_field(:name) %></th>                    
                    <th><%= translate_field(:description) %></th>
                    <th><%= translate_field(:actions) %></th>
                </tr>
            </thead>
            <tbody>
                <% @categories.each do |cat| %>
                    <tr>
                        <td><%= cat.name %></td>                        
                        <td><%= cat.description %></td>
                        <td class="flex gap-2">
                            <%= link_to translate_action(:show), cat, class: "btn btn-dash btn-primary", target: "_blank" %>
                            <%= link_to translate_action(:edit), edit_dashboard_category_path(cat), class: "btn btn-dash btn-secondary" %>
                            <button type="button" class="btn btn-dash btn-error" data-action="click->confirm#open" data-url="<%= dashboard_category_path(cat) %>" data-method="delete" data-name="<%= cat.name %>">
                                <%= translate_action(:delete) %>
                            </button>
                        </td>
                    </tr>
                <% end %>
            </tbody>
        </table>
        <%= render "partials/pagination", pagy: @pagy if @pagy.pages > 1 %>
    </div>        
</div>