<div data-confirm-target="modal" aria-hidden="true">
  <input type="checkbox" id="confirm_modal" class="modal-toggle" />
  <div class="modal" role="presentation">
    <div class="modal-box" role="document" aria-labelledby="confirm-modal-title" aria-describedby="confirm-modal-desc">
      <h3 id="confirm-modal-title" class="font-bold text-lg"><%= translate_common(:confirm, :sure) %></h3>
      <p id="confirm-modal-desc" class="py-2"><%= translate_common(:confirm, :sure2) %> <strong data-confirm-target="name"></strong>.</p>
      <p class="py-2"><%= translate_common(:confirm, :sure3) %></p>
      <div class="modal-action items-center">
        <button type="button" class="btn" data-action="confirm#cancel"><%= translate_action(:cancel) %></button>
        <button type="button" class="btn btn-error flex items-center gap-2" data-confirm-target="confirmButton" data-action="click->confirm#confirm">
          <span class="confirm-spinner hidden" aria-hidden="true"> 
            <svg class="animate-spin h-4 w-4" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
            </svg>
          </span>
          <span class="confirm-text"><%= translate_action(:delete) %></span>
        </button>
      </div>
    </div>
    <label class="modal-backdrop" for="confirm_modal">Close</label>
  </div>
</div>
